import { spawn } from "child_process"
import fs from "fs"
import { Queue } from "bullmq"

import { redis } from "./redis"

export class WorkerController {
	private readonly POLL_INTERVAL_MS = 5000
	private readonly MAX_WORKERS = 5

	private queue: Queue
	public isRunning = false
	private pollingInterval: NodeJS.Timeout | null = null
	private activeWorkers = new Set<string>()

	constructor() {
		this.queue = new Queue("roomote", { connection: redis })
	}

	async start() {
		if (this.isRunning) {
			console.log("Controller is already running")
			return
		}

		this.isRunning = true
		console.log("Worker controller started")

		await this.checkAndSpawnWorker()

		this.pollingInterval = setInterval(async () => {
			await this.checkAndSpawnWorker()
		}, this.POLL_INTERVAL_MS)
	}

	async stop() {
		if (!this.isRunning) {
			return
		}

		this.isRunning = false
		console.log("Stopping worker controller...")

		if (this.pollingInterval) {
			clearInterval(this.pollingInterval)
			this.pollingInterval = null
		}

		await this.queue.close()
		console.log("Worker controller stopped")
	}

	private async checkAndSpawnWorker() {
		try {
			const waiting = await this.queue.getWaiting()
			const active = await this.queue.getActive()

			const waitingCount = waiting.length
			const activeCount = active.length

			console.log(
				`Queue status: ${waitingCount} waiting, ${activeCount} active, ${this.activeWorkers.size} spawned workers`,
			)

			if (waitingCount > 0 && this.activeWorkers.size < this.MAX_WORKERS) {
				await this.spawnWorker()
			}
		} catch (error) {
			console.error("Error checking queue status:", error)
		}
	}

	/**
	 * 获取Docker hosts映射参数
	 * 通过环境变量控制是否启用以及具体的映射内容
	 */
	private getDockerHostMappings(): string[] {
		// 检查是否启用hosts映射
		const enableHostMapping = process.env.ENABLE_HOST_MAPPING === "true"
		if (!enableHostMapping) {
			return []
		}

		// 如果设置了自定义的hosts映射，使用自定义的
		const customHostMappings = process.env.DOCKER_HOST_MAPPINGS
		if (customHostMappings) {
			try {
				// 支持JSON格式的hosts映射配置
				// 例如: [{"host": "download.bt.cn", "ip": "**************"}, ...]
				const hostMappings = JSON.parse(customHostMappings)
				return hostMappings.map(
					(mapping: { host: string; ip: string }) => `--add-host ${mapping.host}:${mapping.ip}`,
				)
			} catch (error) {
				console.warn("Failed to parse DOCKER_HOST_MAPPINGS, using default hosts mapping:", error)
			}
		}

		// 默认的hosts映射（原有的映射）
		return [
			"--add-host download.bt.cn:**************",
			"--add-host dg2.bt.cn:**********",
			"--add-host mirrors.tuna.tsinghua.edu.cn:************",
			"--add-host nexus.cmss.com:**************",
			"--add-host mirrors.bclinux.org:************",
			"--add-host mirrors.bclinux.cn:************",
			"--add-host mirrors.cmecloud.cn:************",
			"--add-host cicdcsy.harbor.cmss.com:**************",
			"--add-host registry.paas:**********",
			"--add-host jira.cmss.com:**************",
			"--add-host conf.cmss.com:**************",
			"--add-host gitlab.cmss.com:**************",
			"--add-host gerrit.cmss.com:**************",
			"--add-host dev.idaas.cmss.com:*************",
			"--add-host aicp.idaas.cmss.com:*************",
			"--add-host gitlab.example2000.com:************",
			"--add-host gerrit.cmssslave2.com:***********",
			"--add-host SZYFQ-POD1-P0F0-PM-OS01-DATAINSIGHT-WEB-001:127.0.0.1",
		]
	}

	private async spawnWorker() {
		const workerId = `worker-${Date.now()}`

		try {
			console.log(`Spawning worker: ${workerId}`)
			const isRunningInDocker = fs.existsSync("/.dockerenv")

			// 清理环境变量，过滤掉 undefined 字符串和空值
			const cleanEnvVar = (value: string | undefined): string | undefined => {
				if (!value || value === 'undefined' || value.trim() === '') {
					return undefined;
				}
				return value.trim();
			};

			// 设置默认值
			const gitlabUrl = cleanEnvVar(process.env.GITLAB_URL) || "http://gitlab.cmss.com";
			const jiraUrl = cleanEnvVar(process.env.JIRA_URL) || "http://jira.cmss.com";

			console.log(`[DEBUG] Controller environment variables:`);
			console.log(`  GITLAB_URL: ${process.env.GITLAB_URL} -> ${gitlabUrl}`);
			console.log(`  GITLAB_PAT: ${process.env.GITLAB_PAT ? 'SET' : 'NOT_SET'}`);
			console.log(`  JIRA_URL: ${process.env.JIRA_URL} -> ${jiraUrl}`);

			const dockerArgs = [
				`--name roomote-${workerId}`,
				"--rm",
				"--network roomote_default",
				"-e HOST_EXECUTION_METHOD=docker",
				`-e GH_TOKEN=${process.env.GH_TOKEN}`,
				`-e DATABASE_URL=${process.env.DATABASE_URL}`,
				`-e REDIS_URL=${process.env.REDIS_URL}`,
				`-e NODE_ENV=${process.env.NODE_ENV}`,
				// GitLab 认证信息
				`-e GITLAB_URL=${gitlabUrl}`,
				`-e GITLAB_PAT=${process.env.GITLAB_PAT}`,
				// JIRA 认证信息
				`-e JIRA_URL=${process.env.JIRA_URL}`,
				`-e JIRA_USERNAME=${process.env.JIRA_USERNAME}`,
				`-e JIRA_PASSWORD=${process.env.JIRA_PASSWORD}`,
				// ZHANLU API 认证信息
				`-e ZHANLU_ACCESS_KEY=${process.env.ZHANLU_ACCESS_KEY}`,
				`-e ZHANLU_SECRET_KEY=${process.env.ZHANLU_SECRET_KEY}`,
				`-e ZHANLU_TOKEN=${process.env.ZHANLU_TOKEN}`,
				// 其他 AI 提供商的 API 密钥
				`-e OPENROUTER_API_KEY=${process.env.OPENROUTER_API_KEY}`,
				`-e SLACK_API_TOKEN=${process.env.SLACK_API_TOKEN}`,
				// 启用评估模式
				`-e EVAL_MODE=${process.env.EVAL_MODE}`,
				// 依赖安装控制环境变量
				`-e SKIP_DEPENDENCY_INSTALL=${process.env.SKIP_DEPENDENCY_INSTALL || "false"}`,
				// 设置日志目录，避免在 Git 工作区创建日志文件
				`-e ZHANLU_LOG_DIR=/var/log/roomote`,
				"-v /var/run/docker.sock:/var/run/docker.sock",
				"-v /tmp/roomote:/var/log/roomote",
				// 添加消息共享目录映射，使MessageBridge能够将消息同步到主机
				"-v /tmp/roomote/messages:/roo/shared/messages",
				// 添加VS Code globalStorage映射
				"-v /tmp/roomote/vscode-storage:/roo/.vscode/User/globalStorage",
				// 动态添加hosts映射
				...this.getDockerHostMappings(),
			]

			const cliCommand = "pnpm worker"

			const command = isRunningInDocker
				? `docker run ${dockerArgs.join(" ")} roomote-worker sh -c "${cliCommand}"`
				: cliCommand

			console.log("Spawning worker with command:", command)

			const childProcess = spawn("sh", ["-c", command], {
				detached: true,
				stdio: ["ignore", "pipe", "pipe"],
			})

			if (childProcess.stdout) {
				childProcess.stdout.on("data", (data) => {
					console.log(data.toString())
				})
			}

			if (childProcess.stderr) {
				childProcess.stderr.on("data", (data) => {
					console.error(data.toString())
				})
			}

			this.activeWorkers.add(workerId)

			childProcess.on("exit", (code) => {
				console.log(`Worker ${workerId} exited with code ${code}`)
				this.activeWorkers.delete(workerId)
			})

			childProcess.on("error", (error) => {
				console.error(`Worker ${workerId} error:`, error)
				this.activeWorkers.delete(workerId)
			})

			// Detach the process so it can run independently.
			childProcess.unref()
		} catch (error) {
			console.error(`Failed to spawn worker ${workerId}:`, error)
			this.activeWorkers.delete(workerId)
		}
	}
}

// Only run if this file is executed directly (not imported).
if (import.meta.url === `file://${process.argv[1]}`) {
	const controller = new WorkerController()

	process.on("SIGTERM", async () => {
		console.log("SIGTERM -> shutting down controller gracefully...")
		await controller.stop()
		process.exit(0)
	})

	process.on("SIGINT", async () => {
		console.log("SIGINT -> shutting down controller gracefully...")
		await controller.stop()
		process.exit(0)
	})

	controller.start().catch((error) => {
		console.error("Failed to start controller:", error)
		process.exit(1)
	})
}
